{% extends "core/base.html" %}

{% block title %}Welcome{% endblock title %}

{% block content %}
    <div class="mt-6 px-6 py-12 bg-gray-100 rounded-xl">
        <h2 class = "mb-12 text-2xl text-center">Newest Arrivals</h2>
        <div class="grid grid-cols-3 gap-3">
            {% for item in items %}
                <div>
                    <a href="{% url 'item:detail' item.id %}">
                        <div>
                            <img src="{{ item.image.url}}" alt="" class="rounded-xl">
                        </div>

                        <div class="p-6 bg-white rounded-b-xl">
                            <h2 class = "text-2xl">{{ item.name }}</h2>
                            <p class="text-gray-500">Price: {{ item.price }}</p>

                        </div>
                    </a>
                </div>
            {% endfor %}
        </div>
    </div>

    <div class="mt-6 px-6 py-12 bg-gray-100 rounded-xl">
        <h2 class = "mb-12 text-2xl text-center">Categories</h2>
        <div class="grid grid-cols-3 gap-3">
            {% for category in categories %}
                <div>
                    <a href="">
                        <div class="p-6 bg-white rounded-b-xl">
                            <h2 class = "text-2xl">{{ category.name }}</h2>
                            <p class="text-gray-500">{{category.items.count }} items</p>

                        </div>
                    </a>
                </div>
            {%endfor%}
        </div>
{% endblock content %}

