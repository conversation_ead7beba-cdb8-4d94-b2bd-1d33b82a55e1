../../Scripts/sqlformat.exe,sha256=EZcEs2BQZpHJhAjGL-d2ip-cpw-qarIQQ05n0lJZpb4,108420
sqlparse-0.5.3.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
sqlparse-0.5.3.dist-info/METADATA,sha256=6h5d9alp7xnWLgXACJamTWruedZZl8jYbtTuOwbprI4,3872
sqlparse-0.5.3.dist-info/RECORD,,
sqlparse-0.5.3.dist-info/WHEEL,sha256=C2FUgwZgiLbznR-k0b_5k3Ai_1aASOXDss3lzCUsUug,87
sqlparse-0.5.3.dist-info/entry_points.txt,sha256=caB1VVIDXYzEjsQD0qpaPl2CfDFIKnRSPpsK88ne_4M,53
sqlparse-0.5.3.dist-info/licenses/AUTHORS,sha256=42p-Fq2--ynHFSZXif0f446g8MBD5i2gyvMclzRU5S4,3385
sqlparse-0.5.3.dist-info/licenses/LICENSE,sha256=wZOCNbgNOekxOOrontw69n4Y7LxA0mZSn6V7Lc5CYxA,1537
sqlparse/__init__.py,sha256=XIHbNzQJrt9dxjOW0ylgG3jzgOVBEVHIrjppI4pZsLg,2325
sqlparse/__main__.py,sha256=1jhVFLHlZs4NUJoAuHvQQKWgykPVTdgeE8V4XB5WQzw,610
sqlparse/__pycache__/__init__.cpython-312.pyc,,
sqlparse/__pycache__/__main__.cpython-312.pyc,,
sqlparse/__pycache__/cli.cpython-312.pyc,,
sqlparse/__pycache__/exceptions.cpython-312.pyc,,
sqlparse/__pycache__/formatter.cpython-312.pyc,,
sqlparse/__pycache__/keywords.cpython-312.pyc,,
sqlparse/__pycache__/lexer.cpython-312.pyc,,
sqlparse/__pycache__/sql.cpython-312.pyc,,
sqlparse/__pycache__/tokens.cpython-312.pyc,,
sqlparse/__pycache__/utils.cpython-312.pyc,,
sqlparse/cli.py,sha256=D2EUGXr9kMzrNOI-yVFnwPnOefed_9N3hU2HgJ3T7Ck,5891
sqlparse/engine/__init__.py,sha256=i9kh0USMjk1bwKPFTn6K0PKC55HOqvnkoxHi1t7YccE,447
sqlparse/engine/__pycache__/__init__.cpython-312.pyc,,
sqlparse/engine/__pycache__/filter_stack.cpython-312.pyc,,
sqlparse/engine/__pycache__/grouping.cpython-312.pyc,,
sqlparse/engine/__pycache__/statement_splitter.cpython-312.pyc,,
sqlparse/engine/filter_stack.py,sha256=qA0MlaTSN3rDa3aUTsW-G5yXbZt6xnCZlEsKTGMO-Uk,1600
sqlparse/engine/grouping.py,sha256=B58WRRfv0XEcZXXdCRxaVyCnJI6KG1cXy0e8Rb5MDwg,14602
sqlparse/engine/statement_splitter.py,sha256=iB-Pcy0_Om9dGnTFP7FvwNytlb07rGDhFNHOtFvnARs,4094
sqlparse/exceptions.py,sha256=QyZ9TKTvzgcmuQ1cJkxAj9SoAw4M02-Bf0CSUNWNDKM,342
sqlparse/filters/__init__.py,sha256=HSlZIjmJBUwjwQf00KDpok1azKOzFVUWXBNpsVLKZ3Y,1343
sqlparse/filters/__pycache__/__init__.cpython-312.pyc,,
sqlparse/filters/__pycache__/aligned_indent.cpython-312.pyc,,
sqlparse/filters/__pycache__/others.cpython-312.pyc,,
sqlparse/filters/__pycache__/output.cpython-312.pyc,,
sqlparse/filters/__pycache__/reindent.cpython-312.pyc,,
sqlparse/filters/__pycache__/right_margin.cpython-312.pyc,,
sqlparse/filters/__pycache__/tokens.cpython-312.pyc,,
sqlparse/filters/aligned_indent.py,sha256=kvN5TVMxovyX6cDnmxF-t-KUz2RnzbQ1fIQzdIxYY2g,5110
sqlparse/filters/others.py,sha256=2m_OXcfIsucJDPK-SetJ3dO7E6R3bw-dYSmZ5KDccBE,6658
sqlparse/filters/output.py,sha256=OMSalSPvq3s3-r268Tjv-AmtjTNCfhLayWtQFO5oyVE,4001
sqlparse/filters/reindent.py,sha256=cgiTOSfW02Ll2vl0pHpStbYtpzsmpTBreFrFDIjXxTg,9906
sqlparse/filters/right_margin.py,sha256=Hil692JB3ZkiMPpPPZcMUiRUjDpmhFiuARUu5_imym8,1543
sqlparse/filters/tokens.py,sha256=CZwDwMzzOdq0qvTRIIic7w59g54QhwFgM2Op9932Zvk,1553
sqlparse/formatter.py,sha256=Cf_vAKmcSN-Wq_6Hz1NBtFjxJl8dyYwcZf772Ecy4MU,7804
sqlparse/keywords.py,sha256=liofhmvhdKKurJ0wf9cE1nyR88O9PQqiff-IZxWZtiA,30504
sqlparse/lexer.py,sha256=IOfwZTxvw0hSfACRA72sbWbwKTkaVSNCzKjR83PTonA,5991
sqlparse/sql.py,sha256=FaZm85uuVKKmqJfpKU7f95A44zXFVzoAgsgO41fD1E8,20846
sqlparse/tokens.py,sha256=g9iwZMLGboSuRQUHfVLSddC5dv43PkxJOu4-tkhMs0o,1779
sqlparse/utils.py,sha256=bKFkat1Ko5wfEIPhD_FvZRMBGG9OJtv9dQLBoZKnboc,3475
