{% extends "core/base.html" %}

{% block title%}{{item.name}}{% endblock title %}

{% block content %}
<div class="grid grid-cols-5 gap-6">
    <div class="cols-span-3">
        <img src="{{item.image.url}}" alt="" class="rounded-xl ">
    </div>

    <div class="col-span-2 p-6 bg-gray-100 rounded-xl">
        <h1 class="text-2xl text-3xl">{{item.name}}</h1>
        <p class="text-gray-500"><strong>Price: </strong>{{item.price}}</p>
        <p class="text-gray-500"><strong>Seller: </strong>{{item.created_by.username}}</p>
        {% if item.description %}
            <p class="text-gray-700">
                <strong>Description: </strong>
                <br>
                {{item.description}}
            </p>
        {% endif %}
        
        <a href="" class='inline-block mt-6 px-4 py-4 font-semibold bg-teal-500 text-white rounded-xl hover:bg-teal-700'>Contact seller</a>
    </div>
</div>
<div class="mt-6 px-6 py-12 bg-gray-100 rounded-xl">
    <h2 class = "mb-12 text-2xl text-center">Related Items</h2>
    <div class="grid grid-cols-3 gap-3 max-w-6xl">
        {% for item in related_items %}
            <div>
                <a href="{% url 'item:detail' item.id %}">
                    <div>
                        <img src="{{ item.image.url}}" alt="" class="rounded-xl">
                    </div>

                    <div class="p-6 bg-white rounded-b-xl">
                        <h2 class = "text-2xl">{{ item.name }}</h2>
                        <p class="text-gray-500">Price: {{ item.price }}</p>

                    </div>
                </a>
            </div>
        {% endfor %}
    </div>
</div>
{% endblock content %}



